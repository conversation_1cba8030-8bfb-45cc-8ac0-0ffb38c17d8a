## How to integration

1. #### Install the Kintaro ms sdk

    **Add the repository to your pom.xml**

    ```xml
      <repositories>
        <repository>
            <id>stationdm-public</id>
            <url>https://nexus3.stationdm.com/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
      </repositories>
    ```

    **Add the dependency to your pom.xml**

    ```xml
    <dependencies> 
        <dependency>
        <groupId>com.stationdm</groupId>
        <artifactId>kintaro2-ms-sdk</artifactId>
        <version>1.0.0</version>
        </dependency>
    </dependencies>
    ```

   **Configure username and password in maven settings.xml**

    ```xml
    <servers>
      <server>
        <id>stationdm-public</id>
        <username>ddevsdm</username>
        <password>This will provide by StationDM</password>
      </server>
    </servers>
    ```

2. #### modify `logback-spring.xml` 

    ```xml
    <?xml version="1.0" encoding="UTF-8"?>
    <!DOCTYPE xml>
    <configuration>
        ...
          <include resource="com/stationdm/log/logback-spring.xml"/>
        ...
    </configuration>
    ```
3. #### Record logs

    **Record Process Log**

    These logs logged in this way will show in Kintaro portal

    - `L.info(String content)`

    - `L.warn(String content)`

    - `L.error(String content)`

4. #### Implement functions

    **Create a new Service Class**

    Create a servive should end the name with `Service` and implements `IWebService`
    It should add annotation @Service
    
    Here is an sample:
    ```java
    @Service
    public class SampleService implements IWebService {
    // ...
    }
    ```

   There can add a new function like:
    ```java
    public Object test(KntReqData reqData) {
        L.info("test log");
        return "OK";
    }
    ```
    Then it can be access by link: 

    http://localhost:{port}/ms/{serviceName}/{functionName}

    http://localhost:8621/ms/sample/test
