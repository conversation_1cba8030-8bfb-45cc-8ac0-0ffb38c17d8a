<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多轮对话测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
            background-color: #ff4757;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background-color: #2ed573;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.ai {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 18px;
            border-radius: 18px;
            word-wrap: break-word;
            position: relative;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.ai .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e1e8ed;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .message.user .message-time {
            text-align: right;
        }

        .typing-indicator {
            display: none;
            padding: 15px;
            font-style: italic;
            color: #666;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #667eea;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e8ed;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 12px 18px;
            border: 2px solid #e1e8ed;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .control-button {
            padding: 8px 16px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .control-button:hover {
            background: #667eea;
            color: white;
        }

        .error-message {
            background: #ff4757;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>AI 多轮对话测试</h1>
            <div>
                <span id="status-text">未连接</span>
                <span id="status-indicator" class="status-indicator"></span>
            </div>
        </div>

        <div class="error-message" id="error-message"></div>

        <div class="chat-messages" id="chat-messages">
            <div class="message ai">
                <div class="message-bubble">
                    <div>你好！我是AI助手，可以帮你查天气、订票等。请问需要什么帮助？</div>
                    <div class="message-time">系统消息</div>
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typing-indicator">
            AI正在输入
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input">
            <div class="input-group">
                <input type="text" id="message-input" class="message-input" 
                       placeholder="输入您的消息..." maxlength="500">
                <button id="send-button" class="send-button">发送</button>
            </div>
            <div class="control-buttons">
                <button class="control-button" onclick="sendQuickMessage('你好')">你好</button>
                <button class="control-button" onclick="sendQuickMessage('天气')">查天气</button>
                <button class="control-button" onclick="sendQuickMessage('订票')">订票</button>
                <button class="control-button" onclick="clearChat()">清空对话</button>
                <button class="control-button" onclick="disconnectChat()">断开连接</button>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let isConnected = false;
        let messageQueue = [];

        const statusText = document.getElementById('status-text');
        const statusIndicator = document.getElementById('status-indicator');
        const chatMessages = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');
        const errorMessage = document.getElementById('error-message');

        function updateStatus(text, connected) {
            statusText.textContent = text;
            isConnected = connected;
            if (connected) {
                statusIndicator.classList.add('connected');
            } else {
                statusIndicator.classList.remove('connected');
            }
            sendButton.disabled = !connected;
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        function addMessage(content, isUser = false, isSystem = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'ai'}`;
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';
            
            const contentDiv = document.createElement('div');
            contentDiv.textContent = content;
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = isSystem ? '系统消息' : new Date().toLocaleTimeString();
            
            bubbleDiv.appendChild(contentDiv);
            bubbleDiv.appendChild(timeDiv);
            messageDiv.appendChild(bubbleDiv);
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTyping(show) {
            typingIndicator.style.display = show ? 'block' : 'none';
            if (show) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }

        function sendMessage(message) {
            if (!message.trim()) return;
            
            addMessage(message, true);
            messageInput.value = '';
            
            if (!isConnected) {
                startWorkflow(message);
            } else {
                // 如果已连接，将消息加入队列等待处理
                messageQueue.push(message);
            }
        }

        function startWorkflow(question) {
            if (isConnected) {
                disconnectChat();
            }

            const url = `/sample/runWorkflow?question=${encodeURIComponent(question)}`;
            
            updateStatus('正在连接...', false);
            showTyping(true);
            
            eventSource = new EventSource(url);
            
            eventSource.onopen = function(event) {
                updateStatus('已连接', true);
                showTyping(false);
            };
            
            eventSource.addEventListener('question', function(event) {
                showTyping(false);
                addMessage(event.data.replace('AI: ', ''));
                showTyping(true);
            });
            
            eventSource.addEventListener('message', function(event) {
                if (event.data.trim()) {
                    showTyping(false);
                    addMessage(event.data);
                }
            });
            
            eventSource.addEventListener('message_end', function(event) {
                showTyping(false);
            });
            
            eventSource.addEventListener('error', function(event) {
                showTyping(false);
                addMessage('❌ ' + event.data);
                showError('发生错误: ' + event.data);
            });
            
            eventSource.addEventListener('complete', function(event) {
                showTyping(false);
                addMessage('✅ ' + event.data, false, true);
                disconnectChat();
            });
            
            eventSource.addEventListener('info', function(event) {
                console.log('Info:', event.data);
            });
            
            eventSource.onerror = function(event) {
                showTyping(false);
                showError('连接错误或中断');
                disconnectChat();
            };
        }

        function disconnectChat() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            updateStatus('未连接', false);
            showTyping(false);
            messageQueue = [];
        }

        function sendQuickMessage(message) {
            messageInput.value = message;
            sendMessage(message);
        }

        function clearChat() {
            chatMessages.innerHTML = `
                <div class="message ai">
                    <div class="message-bubble">
                        <div>你好！我是AI助手，可以帮你查天气、订票等。请问需要什么帮助？</div>
                        <div class="message-time">系统消息</div>
                    </div>
                </div>
            `;
        }

        // 事件监听
        sendButton.addEventListener('click', () => {
            sendMessage(messageInput.value);
        });

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage(messageInput.value);
            }
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', disconnectChat);

        // 初始化
        updateStatus('未连接', false);
    </script>
</body>
</html>
