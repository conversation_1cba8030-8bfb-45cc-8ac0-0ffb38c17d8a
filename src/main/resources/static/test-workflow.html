<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .messages {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            margin-top: 20px;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .message.question {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message.message {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .message.error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .message.complete {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .message.info {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>工作流测试页面</h1>
        
        <div class="input-group">
            <label for="question">输入问题:</label>
            <input type="text" id="question" value="你好" placeholder="请输入您的问题...">
        </div>
        
        <button id="startBtn" onclick="startWorkflow()">开始工作流</button>
        <button id="stopBtn" onclick="stopWorkflow()" disabled>停止工作流</button>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div id="messages" class="messages">
            <div class="message info">等待开始...</div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let isConnected = false;

        function updateStatus(message, isConnected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + (isConnected ? 'connected' : 'disconnected');
        }

        function addMessage(type, content) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            messageDiv.textContent = new Date().toLocaleTimeString() + ' - ' + content;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function startWorkflow() {
            if (isConnected) {
                stopWorkflow();
            }

            const question = document.getElementById('question').value || '你好';
            const url = `/sample/runWorkflow?question=${encodeURIComponent(question)}`;
            
            updateStatus('正在连接...', false);
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            
            // 清空消息
            document.getElementById('messages').innerHTML = '';
            
            eventSource = new EventSource(url);
            
            eventSource.onopen = function(event) {
                isConnected = true;
                updateStatus('已连接', true);
                addMessage('info', '工作流已启动，问题: ' + question);
            };
            
            eventSource.addEventListener('question', function(event) {
                addMessage('question', event.data);
            });
            
            eventSource.addEventListener('message', function(event) {
                addMessage('message', event.data);
            });
            
            eventSource.addEventListener('message_end', function(event) {
                addMessage('info', event.data);
            });
            
            eventSource.addEventListener('error', function(event) {
                addMessage('error', event.data);
            });
            
            eventSource.addEventListener('complete', function(event) {
                addMessage('complete', event.data);
                stopWorkflow();
            });
            
            eventSource.addEventListener('info', function(event) {
                addMessage('info', event.data);
            });
            
            eventSource.onerror = function(event) {
                addMessage('error', '连接错误或中断');
                stopWorkflow();
            };
        }

        function stopWorkflow() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            isConnected = false;
            updateStatus('已断开连接', false);
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            addMessage('info', '工作流已停止');
        }

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            stopWorkflow();
        });

        // 回车键启动工作流
        document.getElementById('question').addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && !isConnected) {
                startWorkflow();
            }
        });
    </script>
</body>
</html>
