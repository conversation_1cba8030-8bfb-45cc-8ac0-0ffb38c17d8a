<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xml>
<configuration>

	<include resource="com/stationdm/log/logback-spring.xml"/>

	<springProperty scope="context" name="KNT_SYS_LOG_DIR" source="knt.logback.file.path" defaultValue="/var/log/kintaro"/>
	<springProperty scope="context" name="KNT_LOG_FILE" source="knt.logback.file.name" defaultValue="boot.log"/>
	<springProperty scope="context" name="KNT_LOG_LEVEL" source="knt.logback.file.level" defaultValue="INFO"/>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder charset="UTF-8">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{80} %msg%n</pattern>
		</encoder>
	</appender>
	<appender name="LOG_ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${KNT_SYS_LOG_DIR}/${KNT_LOG_FILE}</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${KNT_SYS_LOG_DIR}/zipped/${KNT_LOG_FILE}_%d{yyyyMMdd}_%i.zip</fileNamePattern>
				<maxFileSize>500MB</maxFileSize>
			<maxHistory>458</maxHistory>
		</rollingPolicy>
		
		<encoder charset="UTF-8">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{80} %msg%n</pattern>
		</encoder>
	</appender>

	<logger name="org.hibernate" level="OFF">
	</logger>
	<root level="${KNT_LOG_LEVEL}">
		<appender-ref ref="LOG_ROLLING" />
		<appender-ref ref="STDOUT" />
	</root>
</configuration>