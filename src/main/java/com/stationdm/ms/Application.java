package com.stationdm.ms;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.reactive.config.EnableWebFlux;

/**
 * <AUTHOR>
 * @date 2021-03-29 18:06
 * @type Application
 * @description
 */
@ComponentScan(basePackages = {
        "com.stationdm.workflow",
        "com.stationdm.ms"
})
@EnableWebFlux
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
