package com.stationdm.ms.service;

import com.stationdm.log.L;
import com.stationdm.ms.base.IWebService;
import com.stationdm.ms.base.data.KntReqData;
import com.stationdm.ms.base.exception.CommonKntException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021-05-21 17:12
 * @type SampleServiceImpl
 * @description
 */
@Service
public class SampleServiceImpl implements IWebService {

    public String test(KntReqData reqData) {
        L.info("test log");
        return "OK";
    }

    public Object error(KntReqData reqData) {
        L.info("error log");
        if (1 == 1) {
            throw new CommonKntException("error log");
        }
        return "OK";
    }
}
