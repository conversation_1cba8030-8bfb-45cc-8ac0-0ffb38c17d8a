package com.stationdm.ms.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.ChatNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 多轮交互示例 - 模仿原始写法
 * 核心模式：
 * return requestUserInput(result, context)
 *     .flatMap(input -> {
 *         firstInputNode.put("value", input);
 *         return execute(context);
 *     });
 * <AUTHOR>
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
public class MultiRoundInteractionExample extends ChatNode {

    private JsonNode input;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        // 获取用户输入
        ArrayNode inputArrayNode = (ArrayNode) input;
        ObjectNode firstInputNode = (ObjectNode) inputArrayNode.get(0);
        String userInput = firstInputNode.get("value").asText();
        
        // 处理用户输入并生成响应
        String result = processInput(userInput, context);
        
        // 核心多轮交互逻辑：如果需要继续交互，则请求用户输入
        if (shouldContinue(result, context)) {
            // 抛一个事件给外部
            return requestUserInput(result, context)
                    .flatMap(nextInput -> {
                        firstInputNode.put("value", nextInput);
                        return execute(context);
                    });
        }
        
        // 交互结束
        return Mono.empty();
    }
    
    /**
     * 处理输入的核心逻辑
     */
    private String processInput(String userInput, WorkflowContext context) {
        // 示例：订餐场景的多轮交互
        if (userInput.contains("订餐") || userInput.contains("点餐")) {
            return "好的，请问您想要什么菜系？中餐、西餐还是日料？";
        }
        
        if (userInput.contains("中餐")) {
            context.setVariable("cuisine", "中餐");
            return "您选择了中餐。请问您想要什么口味？川菜、粤菜还是湘菜？";
        }
        
        if (userInput.contains("川菜")) {
            context.setVariable("flavor", "川菜");
            return "您选择了川菜。请问您的预算是多少？";
        }
        
        if (userInput.matches("\\d+")) {
            context.setVariable("budget", userInput);
            String cuisine = (String) context.getVariable("cuisine");
            String flavor = (String) context.getVariable("flavor");
            return String.format("好的，为您推荐预算%s元的%s%s。订餐完成！", userInput, cuisine, flavor);
        }
        
        return "请提供有效的信息。";
    }
    
    /**
     * 判断是否应该继续交互
     */
    private boolean shouldContinue(String result, WorkflowContext context) {
        // 如果包含"完成"则结束
        return !result.contains("完成");
    }
}
