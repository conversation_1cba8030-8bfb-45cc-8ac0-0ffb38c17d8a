package com.stationdm.ms.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.stationdm.log.L;
import com.stationdm.ms.base.data.KntRes;
import com.stationdm.ms.base.exception.KntException;
import com.stationdm.ms.pojo.request.SampleRequest;
import com.stationdm.workflow.engine.WorkflowBuilder;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.container.SequenceContainer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2021-05-21 17:12
 * @type SampleController
 * @description
 */
@RestController
@RequestMapping("/sample")
public class SampleController {

    final WorkflowBuilder workflowBuilder;
    final ApplicationContext applicationContext;

    public SampleController(WorkflowBuilder workflowBuilder, ApplicationContext applicationContext) {
        this.workflowBuilder = workflowBuilder;
        this.applicationContext = applicationContext;
    }

    @GetMapping("/")
    public String home() {
        return "Welcome to kintaro2-ms-sample!";
    }

    @RequestMapping(value = "/test123", method = RequestMethod.GET)
    public KntRes<String> test123() {

        return KntRes.error(new KntException("aaaa", "3210"));
    }

    @RequestMapping(value = "/test", method = RequestMethod.POST)
    public KntRes<String> test(@RequestBody SampleRequest request) {
        L.info("execute test controller");
        return KntRes.ok(request.getName());
    }

    @RequestMapping(value = "/testHello/{name}", method = RequestMethod.POST)
    public KntRes<String> test(@PathVariable String name) throws InterruptedException {
        Thread.sleep(2000);
        L.info("execute testHello controller");
        return KntRes.ok(name + "Hello World");
    }

    @PostMapping(value = "/run")
    public KntRes<String> run() throws JsonProcessingException {
        String json ="{\"connections\":[{\"id\":\"start\",\"type\":\"START\"},{\"id\":\"http_request\",\"type\":\"SIMPLE_MULTI_ROUND\"},{\"id\":\"end\",\"type\":\"END\"}],\"nodes\":[{\"id\":\"start\",\"name\":\"Start\",\"type\":\"START\",\"input\":[{\"name\":\"string\",\"type\":\"string\"}]},{\"id\":\"end\",\"name\":\"End\",\"type\":\"END\",\"output\":[{\"name\":\"\",\"type\":\"string\"}]},{\"id\":\"http_request\",\"name\":\"HTTP Request\",\"type\":\"SIMPLE_MULTI_ROUND\",\"input\":[{\"name\":\"query\",\"type\":\"string\",\"value\":\"{{$start.string}}\"}]}]}";
        return KntRes.ok();
    }


}
