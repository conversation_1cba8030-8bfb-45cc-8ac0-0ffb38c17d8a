package com.stationdm.ms.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.stationdm.log.L;
import com.stationdm.ms.base.data.KntRes;
import com.stationdm.ms.base.exception.KntException;
import com.stationdm.ms.pojo.request.SampleRequest;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.engine.WorkflowEngineService;
import com.stationdm.workflow.handler.ChatHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 * @date 2021-05-21 17:12
 * @type SampleController
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/sample")
public class SampleController {

    final WorkflowEngineService workflowEngineService;
    final ApplicationContext applicationContext;
    final ChatHandler chatHandler;

    public SampleController(WorkflowEngineService workflowEngineService, ApplicationContext applicationContext, ChatHandler chatHandler) {
        this.workflowEngineService = workflowEngineService;
        this.applicationContext = applicationContext;
        this.chatHandler = chatHandler;
    }

    @GetMapping("/")
    public String home() {
        return "Welcome to kintaro2-ms-sample!";
    }

    @RequestMapping(value = "/test123", method = RequestMethod.GET)
    public KntRes<String> test123() {

        return KntRes.error(new KntException("aaaa", "3210"));
    }

    @RequestMapping(value = "/test", method = RequestMethod.POST)
    public KntRes<String> test(@RequestBody SampleRequest request) {
        L.info("execute test controller");
        return KntRes.ok(request.getName());
    }

    @RequestMapping(value = "/testHello/{name}", method = RequestMethod.POST)
    public KntRes<String> test(@PathVariable String name) throws InterruptedException {
        Thread.sleep(2000);
        L.info("execute testHello controller");
        return KntRes.ok(name + "Hello World");
    }

    @PostMapping(value = "/run")
    public KntRes<String> run() throws JsonProcessingException {
        String json = "{\"connections\":[{\"id\":\"start\",\"type\":\"START\"},{\"id\":\"http_request\",\"type\":\"SIMPLE_MULTI_ROUND\"},{\"id\":\"end\",\"type\":\"END\"}],\"nodes\":[{\"id\":\"start\",\"name\":\"Start\",\"type\":\"START\",\"input\":[{\"name\":\"string\",\"type\":\"string\"}]},{\"id\":\"end\",\"name\":\"End\",\"type\":\"END\",\"output\":[{\"name\":\"\",\"type\":\"string\"}]},{\"id\":\"http_request\",\"name\":\"HTTP Request\",\"type\":\"SIMPLE_MULTI_ROUND\",\"input\":[{\"name\":\"query\",\"type\":\"string\",\"value\":\"{{$start.string}}\"}]}]}";
        return KntRes.ok();
    }

    /**
     * 简单的流式工作流运行方法
     * 模仿原始的 sub 方法实现
     */
    @PostMapping(value = "/runWorkflow", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> runWorkflow(@RequestParam(defaultValue = "你好") String question) {
        // 直接写死的工作流 JSON
        String json = "{\"connections\":[{\"id\":\"start\",\"type\":\"START\"},{\"id\":\"http_request\",\"type\":\"SIMPLE_MULTI_ROUND\"},{\"id\":\"end\",\"type\":\"END\"}],\"nodes\":[{\"id\":\"start\",\"name\":\"Start\",\"type\":\"START\",\"input\":[{\"name\":\"string\",\"type\":\"string\"}]},{\"id\":\"end\",\"name\":\"End\",\"type\":\"END\",\"output\":[{\"name\":\"\",\"type\":\"string\"}]},{\"id\":\"http_request\",\"name\":\"HTTP Request\",\"type\":\"SIMPLE_MULTI_ROUND\",\"input\":[{\"name\":\"query\",\"type\":\"string\",\"value\":\"{{$start.string}}\"}]}]}";

        return Flux.create(sink -> {
            WorkflowContext context = new WorkflowContext(applicationContext);
            context.setSessionId("sample-session-" + System.currentTimeMillis());

            log.info("开始执行工作流，问题: {}", question);

            // 订阅事件流
            final Disposable eventSub = context.getEventStream().subscribe(
                    event -> {
                        final String eventType = event.getEventType();
                        String eventData = String.valueOf(event.getData());

                        log.info("收到事件: {} - {}", eventType, eventData);

                        switch (eventType) {
                            case "REQUEST_USER_INPUT": {
                                // 用户输入请求事件
                                sink.next(ServerSentEvent.<String>builder()
                                        .event("question")
                                        .data("AI: " + eventData)
                                        .build());
                                break;
                            }
                            case "NODE_MESSAGE_CHUNK": {
                                // 流式消息块
                                sink.next(ServerSentEvent.<String>builder()
                                        .event("message")
                                        .data(eventData)
                                        .build());
                                break;
                            }
                            case "NODE_MESSAGE_END": {
                                // 消息结束
                                sink.next(ServerSentEvent.<String>builder()
                                        .event("message_end")
                                        .data("消息结束")
                                        .build());
                                break;
                            }
                            case "NODE_MESSAGE_ERROR": {
                                // 错误消息
                                sink.next(ServerSentEvent.<String>builder()
                                        .event("error")
                                        .data("错误: " + eventData)
                                        .build());
                                break;
                            }
                            case "WORKFLOW_COMPLETE": {
                                // 工作流完成
                                log.info("工作流执行完成");
                                sink.next(ServerSentEvent.<String>builder()
                                        .event("complete")
                                        .data("工作流执行完成")
                                        .build());
                                sink.complete();
                                break;
                            }
                            default: {
                                // 其他事件
                                sink.next(ServerSentEvent.<String>builder()
                                        .event("info")
                                        .data(eventType + ": " + eventData)
                                        .build());
                                break;
                            }
                        }
                    },
                    error -> {
                        log.error("事件流错误", error);
                        sink.next(ServerSentEvent.<String>builder()
                                .event("error")
                                .data("事件流错误: " + error.getMessage())
                                .build());
                        sink.complete();
                    },
                    sink::complete
            );

            try {
                // 执行工作流
                workflowEngineService.execute(
                                json,
                                question,
                                context
                        )
                        .subscribe(
                                v -> {
                                },
                                e -> {
                                    log.error("工作流执行失败", e);
                                    sink.next(ServerSentEvent.<String>builder()
                                            .event("error")
                                            .data("工作流执行失败: " + e.getMessage())
                                            .build());
                                    context.emitStreamingEvent("WORKFLOW_COMPLETE", "engine", "error");
                                    context.completeEventStream();
                                }
                        );
            } catch (Exception e) {
                log.error("工作流配置加载失败", e);
                sink.next(ServerSentEvent.<String>builder()
                        .event("error")
                        .data("工作流配置加载失败: " + e.getMessage())
                        .build());
                sink.complete();
            }

            // 清理资源
            sink.onCancel(() -> {
                if (!eventSub.isDisposed()) {
                    eventSub.dispose();
                }
            });
            sink.onDispose(() -> {
                if (!eventSub.isDisposed()) {
                    eventSub.dispose();
                }
            });
        }, FluxSink.OverflowStrategy.BUFFER);
    }


    @PostMapping("/user-input")
    public Mono<String> submitUserInput(@RequestParam String interactionId, @RequestParam String answer) {
        boolean success = chatHandler.submitUserInput(interactionId, answer);
        return Mono.just(success ? "提交成功" : "提交失败");
    }


}
