package com.stationdm.ms.controller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.stationdm.log.L;
import com.stationdm.ms.base.data.KntRes;
import com.stationdm.ms.base.exception.KntException;
import com.stationdm.ms.pojo.request.SampleRequest;
import com.stationdm.workflow.engine.WorkflowBuilder;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.container.SequenceContainer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2021-05-21 17:12
 * @type SampleController
 * @description
 */
@RestController
@RequestMapping("/sample")
public class SampleController {

    final WorkflowBuilder workflowBuilder;
    final ApplicationContext applicationContext;

    public SampleController(WorkflowBuilder workflowBuilder, ApplicationContext applicationContext) {
        this.workflowBuilder = workflowBuilder;
        this.applicationContext = applicationContext;
    }

    @GetMapping("/")
    public String home() {
        return "Welcome to kintaro2-ms-sample!";
    }

    @RequestMapping(value = "/test123", method = RequestMethod.GET)
    public KntRes<String> test123() {

        return KntRes.error(new KntException("aaaa", "3210"));
    }

    @RequestMapping(value = "/test", method = RequestMethod.POST)
    public KntRes<String> test(@RequestBody SampleRequest request) {
        L.info("execute test controller");
        return KntRes.ok(request.getName());
    }

    @RequestMapping(value = "/testHello/{name}", method = RequestMethod.POST)
    public KntRes<String> test(@PathVariable String name) throws InterruptedException {
        Thread.sleep(2000);
        L.info("execute testHello controller");
        return KntRes.ok(name + "Hello World");
    }

    @PostMapping(value = "/run")
    public KntRes<String> run() throws JsonProcessingException {
        SequenceContainer sequenceContainer = workflowBuilder.build("{\"nodes\":[{\"id\":\"start\",\"name\":\"Start\",\"type\":\"START\",\"input\":[{\"name\":\"abc\",\"type\":\"string\",\"value\":\"123\"}]},{\"id\":\"intent_detection\",\"name\":\"Intent Detection\",\"type\":\"INTENT_DETECTION\",\"model\":\"GPT-4o\",\"input\":[{\"name\":\"query\",\"type\":\"string\",\"value\":\"{{$start.abc}}\"}],\"output\":[{\"value\":\"Weather\",\"description\":\"The user is asking about the current weather, forecast, temperature, or other meteorological conditions in a specific location.\"},{\"value\":\"Other\",\"description\":\"The user's request does not fall under any predefined intent category such as weather or news. This includes general conversation, undefined queries, or unsupported topics.\"}],\"prompt\":\"{取输入框实际值}\"},{\"id\":\"generate\",\"name\":\"Generate\",\"type\":\"GENERATE\",\"model\":\"目前缺失\",\"input\":[{\"name\":\"weather_day\",\"type\":\"string\",\"value\":\"$http_request.weather_day\"},{\"name\":\"temp_high\",\"type\":\"string\",\"value\":\"$http_request.temp_high\"},{\"name\":\"temp_low\",\"type\":\"string\",\"value\":\"$http_request.temp_low\"}],\"output\":[{\"name\":\"response\",\"type\":\"string\"}],\"prompt\":\"{取输入框实际值}\"},{\"id\":\"end\",\"name\":\"End\",\"type\":\"END\",\"output\":[{\"name\":\"response\",\"type\":\"string\",\"value\":\"$generate.response\"},{\"name\":\"response1\",\"type\":\"string\",\"value\":\"$generate_1.response1\"}]}],\"connections\":[{\"id\":\"start\",\"type\":\"START\"},{\"id\":\"intent_detection\",\"type\":\"INTENT_DETECTION\",\"children\":[{\"id\":\"branch_sequence_1\",\"type\":\"SEQUENCE\",\"children\":[{\"id\":\"generate\",\"type\":\"GENERATE\"}]},{\"id\":\"branch_sequence_2\",\"type\":\"SEQUENCE\",\"children\":[{\"id\":\"generate\",\"type\":\"GENERATE\"}]}]},{\"id\":\"end\",\"type\":\"END\"}]}");
        WorkflowContext workflowContext = new WorkflowContext(applicationContext);
        sequenceContainer.execute(workflowContext)
                .subscribe();
        return KntRes.ok();
    }


}
