package com.stationdm.ms.controller;

import com.stationdm.workflow.registry.NodeTypeRegistry;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyWorkflowConfig {

    @Autowired
    private NodeTypeRegistry nodeTypeRegistry;

    @PostConstruct
    public void registerCustomNodes() {
        nodeTypeRegistry.registerNodeType("QA_CHAT", QaChatNode.class);
    }

}

