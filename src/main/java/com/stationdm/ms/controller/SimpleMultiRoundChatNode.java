package com.stationdm.ms.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.ChatNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 简化版多轮交互聊天节点
 * 模仿原始写法实现多轮交互
 * <AUTHOR>
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
public class SimpleMultiRoundChatNode extends ChatNode {

    private JsonNode input;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        // 获取用户输入
        ArrayNode inputArrayNode = (ArrayNode) input;
        ObjectNode firstInputNode = (ObjectNode) inputArrayNode.get(0);
        String userInput = firstInputNode.get("value").asText();
        
        log.info("收到用户输入: {}", userInput);
        
        // 简单的多轮交互逻辑
        String result = handleUserInput(userInput, context);
        
        // 判断是否需要继续交互
        if (needMoreInput(result)) {
            // 抛一个事件给外部，继续多轮交互
            return requestUserInput(result, context)
                    .flatMap(nextInput -> {
                        firstInputNode.put("value", nextInput);
                        return execute(context);
                    });
        } else {
            // 交互完成，结束流程
            log.info("交互完成: {}", result);
            return Mono.empty();
        }
    }
    
    /**
     * 处理用户输入
     */
    private String handleUserInput(String userInput, WorkflowContext context) {
        // Case 1: 收集用户信息的多轮交互
        if (!context.hasVariable("userName")) {
            context.setVariable("userName", userInput);
            return "您好 " + userInput + "！请问您的年龄是多少？";
        }
        
        if (!context.hasVariable("userAge")) {
            context.setVariable("userAge", userInput);
            return "好的，您今年 " + userInput + " 岁。请问您来自哪个城市？";
        }
        
        if (!context.hasVariable("userCity")) {
            context.setVariable("userCity", userInput);
            String userName = (String) context.getVariable("userName");
            String userAge = (String) context.getVariable("userAge");
            return "谢谢！" + userName + "，" + userAge + "岁，来自" + userInput + "。信息收集完成！";
        }
        
        return "信息已收集完成，谢谢您的配合！";
    }
    
    /**
     * 判断是否需要更多输入
     */
    private boolean needMoreInput(String result) {
        // 如果结果包含问号，说明还需要用户回答
        return result.contains("？") && !result.contains("完成");
    }
}
