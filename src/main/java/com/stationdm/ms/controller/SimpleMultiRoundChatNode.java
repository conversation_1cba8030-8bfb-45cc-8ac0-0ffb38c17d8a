package com.stationdm.ms.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.engine.annotation.Evaluate;
import com.stationdm.workflow.flow.base.ChatNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 简化版多轮交互聊天节点
 * 模仿原始写法实现多轮交互
 * <AUTHOR>
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
public class SimpleMultiRoundChatNode extends ChatNode {

    @Evaluate
    private JsonNode input;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        // 获取用户输入
        ArrayNode inputArrayNode = (ArrayNode) input;
        ObjectNode firstInputNode = (ObjectNode) inputArrayNode.get(0);
        String userInput = firstInputNode.get("value").asText();
        
        log.info("收到用户输入: {}", userInput);
        
        // 简单的多轮交互逻辑
        String result = handleUserInput(userInput, context);
        
        // 判断是否需要继续交互
        if (needMoreInput(result)) {
            // 抛一个事件给外部，继续多轮交互
            return requestUserInput(result, context)
                    .flatMap(nextInput -> {
                        firstInputNode.put("value", nextInput);
                        return execute(context);
                    });
        } else {
            // 交互完成，结束流程
            log.info("交互完成: {}", result);
            return Mono.empty();
        }
    }
    
    /**
     * 处理用户输入 - 直接写死的多轮交互逻辑
     */
    private String handleUserInput(String userInput, WorkflowContext context) {
        // 直接根据用户输入内容判断交互阶段
        if (userInput.contains("你好") || userInput.contains("开始")) {
            return "请问您需要什么帮助？";
        }

        if (userInput.contains("天气")) {
            return "请问您想查询哪个城市的天气？";
        }

        if (userInput.contains("北京") || userInput.contains("上海") || userInput.contains("广州")) {
            return "好的，为您查询" + userInput + "的天气。今天晴，温度25度。还需要其他帮助吗？";
        }

        if (userInput.contains("订票")) {
            return "请问您要订什么票？火车票还是飞机票？";
        }

        if (userInput.contains("火车票")) {
            return "请问您的出发地和目的地是哪里？";
        }

        if (userInput.contains("到")) {
            return "好的，已为您查询相关车次信息。预订完成！";
        }

        if (userInput.contains("谢谢") || userInput.contains("再见")) {
            return "不客气，祝您生活愉快！对话结束。";
        }

        return "我没有理解您的意思，请重新说一遍。";
    }
    
    /**
     * 判断是否需要更多输入
     */
    private boolean needMoreInput(String result) {
        // 如果结果包含问号，说明还需要用户回答
        return result.contains("？") && !result.contains("完成");
    }
}
