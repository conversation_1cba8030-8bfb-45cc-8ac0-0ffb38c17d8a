
package com.stationdm.ms.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.ChatNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 多轮交互聊天节点示例
 * <AUTHOR>
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
public class QaChatNode extends ChatNode {

    private JsonNode input;
    private int maxRounds = 5; // 最大交互轮数，防止无限循环

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        // 获取当前交互轮数
        Integer currentRound = (Integer) context.getVariable("currentRound");
        if (currentRound == null) {
            currentRound = 1;
        }

        log.info("开始第 {} 轮交互", currentRound);

        // 检查是否超过最大轮数
        if (currentRound > maxRounds) {
            log.warn("已达到最大交互轮数 {}, 结束对话", maxRounds);
            return Mono.empty();
        }

        // 获取用户输入
        ArrayNode inputArrayNode = (ArrayNode) input;
        ObjectNode firstInputNode = (ObjectNode) inputArrayNode.get(0);
        String userInput = firstInputNode.get("value").asText();

        log.info("用户输入: {}", userInput);

        // 模拟处理逻辑 - 这里可以根据实际需求进行修改
        String result = processUserInput(userInput, currentRound, context);

        // 判断是否需要继续交互
        if (shouldContinueInteraction(result, currentRound)) {
            // 更新交互轮数
            context.setVariable("currentRound", currentRound + 1);

            // 抛一个事件给外部，继续多轮交互
            return requestUserInput(result, context)
                    .flatMap(nextInput -> {
                        firstInputNode.put("value", nextInput);
                        return execute(context);
                    });
        } else {
            // 交互结束，返回最终结果
            log.info("交互结束，最终结果: {}", result);
            context.setVariable("finalResult", result);
            return Mono.empty();
        }
    }

    /**
     * 处理用户输入的业务逻辑
     */
    private String processUserInput(String userInput, int currentRound, WorkflowContext context) {
        // 这里是你的业务逻辑处理
        switch (currentRound) {
            case 1:
                if (userInput.contains("天气")) {
                    return "请问您想查询哪个城市的天气？";
                } else if (userInput.contains("订票")) {
                    return "请问您想订什么票？火车票还是飞机票？";
                } else {
                    return "我没有理解您的需求，请重新描述一下。";
                }
            case 2:
                String previousContext = (String) context.getVariable("previousInput");
                if (previousContext != null && previousContext.contains("天气")) {
                    context.setVariable("city", userInput);
                    return "好的，您想查询" + userInput + "的天气。请问您想查询哪一天的天气？";
                } else if (previousContext != null && previousContext.contains("订票")) {
                    context.setVariable("ticketType", userInput);
                    return "好的，您想订" + userInput + "。请问出发地是哪里？";
                }
                return "请提供更多信息。";
            case 3:
                String city = (String) context.getVariable("city");
                String ticketType = (String) context.getVariable("ticketType");
                if (city != null) {
                    return "好的，为您查询" + city + userInput + "的天气信息。查询完成！";
                } else if (ticketType != null) {
                    context.setVariable("departure", userInput);
                    return "出发地：" + userInput + "。请问目的地是哪里？";
                }
                return "请提供更多信息。";
            default:
                return "感谢您的使用，对话结束。";
        }
    }

    /**
     * 判断是否需要继续交互
     */
    private boolean shouldContinueInteraction(String result, int currentRound) {
        // 如果结果包含"完成"或"结束"，则停止交互
        if (result.contains("完成") || result.contains("结束")) {
            return false;
        }

        // 如果达到最大轮数，停止交互
        if (currentRound >= maxRounds) {
            return false;
        }

        // 其他情况继续交互
        return true;
    }
}