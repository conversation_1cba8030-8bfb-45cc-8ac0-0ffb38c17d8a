
package com.stationdm.ms.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.ChatNode;
import com.stationdm.workflow.flow.base.ChoiceNode;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
@Data
public class QaChatNode  extends ChatNode {

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        return null;
    }
}