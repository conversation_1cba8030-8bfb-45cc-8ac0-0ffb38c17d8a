@echo off

set attempts=0
set max_attempts=3

:login_loop
if %attempts% geq %max_attempts% goto login_fail

docker login -u "mdevsdm" -p "&!IbO5CP1e1##ek$" https://dockers.stationdm.com
if %ERRORLEVEL% equ 0 (
    echo Docker hub login success!
    goto compile_microservice
) else (
    echo Docker hub login failed, please check your username and password.
    set /a attempts+=1
    goto login_loop
)

:login_fail
echo Failed to log in for %max_attempts% consecutive times, exit.
exit /b 1

:compile_microservice
echo Start compile microservice.
call mvn clean package
if %ERRORLEVEL% neq 0 (
    echo mvn clean package failed.
    exit /b %ERRORLEVEL%
)

echo Start build and push Docker image
set /p image_name=Please input your microservice name:
set /p image_version=Please input your version:
docker build -f Dockerfile -t %image_name%:%image_version% .
docker tag %image_name%:%image_version% dockers.stationdm.com/knt-server/%image_name%:%image_version%
docker push dockers.stationdm.com/knt-server/%image_name%:%image_version%

docker logout https://dockers.stationdm.com

echo Your extension deploy success!
