#!/bin/bash

attempts=0
max_attempts=3

while [ $attempts -lt $max_attempts ]; do
    echo
    docker login -u "mdevsdm" -p "&!IbO5CP1e1##ek$" https://dockers.stationdm.com
    if [ $? -eq 0 ]; then
        echo "Docker hub login success!"
        break
    else
        echo "Docker hub login failed，please check your username and password."
        attempts=$((attempts + 1))
    fi
done

if [ $attempts -eq $max_attempts ]; then
    echo "Failed to log in for $max_attempts consecutive times, exit."
    exit 1
fi

echo "Start compile microservice."
mvn clean package

echo "Start build and push Docker image"
read -p "Please input your microservice name: " image_name
read -p "Please input your version: " image_version
docker build -f Dockerfile -t ${image_name}:${image_version} .
docker tag ${image_name}:${image_version} dockers.stationdm.com/knt-server/${image_name}:${image_version}
docker push dockers.stationdm.com/knt-server/${image_name}:${image_version}

docker logout https://dockers.stationdm.com

echo "Your extension deploy success!"