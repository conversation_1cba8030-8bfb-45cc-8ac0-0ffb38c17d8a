<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.stationdm</groupId>
    <artifactId>kintaro2-ms-sample</artifactId>
    <version>1.0.1</version>
    <packaging>jar</packaging>

    <name>kintaro2-ms-sample</name>
    <description>Kintaro2 MicroService Sample</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.0.2</version>
        <relativePath />
    </parent>

    <repositories>
        <repository>
            <id>stationdm-release</id>
            <url>https://nexus3.stationdm.com/repository/maven-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>stationdm-snapshots</id>
            <url>https://nexus3.stationdm.com/repository/maven-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>stationdm-public</id>
            <url>https://nexus3.stationdm.com/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <maven.test.skip>true</maven.test.skip>
        <maven.build.timestamp.format>yyyyMMdd</maven.build.timestamp.format>
        <kintaro2.mssdk.version>1.0.0</kintaro2.mssdk.version>
    </properties>
    <!--各模块公共依赖-->
    <dependencies>
        <dependency>
            <groupId>com.stationdm.workflow</groupId>
            <artifactId>kintaro2-workflow</artifactId>
            <version>1.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.stationdm</groupId>
            <artifactId>kintaro2-ms-sdk</artifactId>
            <version>${kintaro2.mssdk.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
